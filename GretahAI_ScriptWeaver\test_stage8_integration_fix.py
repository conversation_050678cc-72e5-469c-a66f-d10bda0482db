#!/usr/bin/env python3
"""
Test script to verify Stage 8 integration fix.

This script tests the complete Stage 8 integration to ensure the optimization
button appears and functions correctly within the application workflow.
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Set up logging for the integration test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger("Stage8IntegrationTest")

def test_stage8_detection_logic():
    """Test the Stage 8 detection logic in app.py."""
    logger = logging.getLogger("Stage8IntegrationTest")
    
    try:
        from state_manager import StateManager
        
        logger.info("=" * 60)
        logger.info("TESTING STAGE 8 DETECTION LOGIC")
        logger.info("=" * 60)
        
        # Create a state manager with all prerequisites
        state = StateManager()
        
        # Set up a complete scenario where Stage 8 should be triggered
        state.all_steps_done = True
        state.combined_script_path = "test_script.py"
        state.combined_script_content = "# Test script content"
        state.previous_scripts = {"1": "script1", "2": "script2"}
        state.optimization_in_progress = False
        state.optimization_complete = False
        
        logger.info("Test scenario setup:")
        logger.info(f"  - all_steps_done: {state.all_steps_done}")
        logger.info(f"  - combined_script_path: {state.combined_script_path}")
        logger.info(f"  - combined_script_content: {len(state.combined_script_content)} chars")
        logger.info(f"  - previous_scripts: {len(state.previous_scripts)} scripts")
        logger.info(f"  - optimization_in_progress: {state.optimization_in_progress}")
        logger.info(f"  - optimization_complete: {state.optimization_complete}")
        
        # Test the detection logic (simulate app.py conditions)
        should_run_stage8 = False
        stage8_reason = ""
        
        # Condition 4: All steps done and ready for optimization
        if (hasattr(state, 'all_steps_done') and state.all_steps_done and
            not getattr(state, 'optimization_in_progress', False) and
            not getattr(state, 'optimization_complete', False)):
            
            # Check if we have a combined script, or if we can create one
            has_combined_script = (hasattr(state, 'combined_script_path') and state.combined_script_path and
                                  hasattr(state, 'combined_script_content') and state.combined_script_content)
            
            has_previous_scripts = (hasattr(state, 'previous_scripts') and state.previous_scripts)
            
            if has_combined_script or has_previous_scripts:
                should_run_stage8 = True
                stage8_reason = "all steps done, ready for optimization"
                logger.info("✅ Stage 8 detection logic PASSED - would trigger Stage 8")
                logger.info(f"   Reason: {stage8_reason}")
                logger.info(f"   has_combined_script: {has_combined_script}")
                logger.info(f"   has_previous_scripts: {has_previous_scripts}")
            else:
                logger.error("❌ Stage 8 detection logic FAILED - missing scripts")
        else:
            logger.error("❌ Stage 8 detection logic FAILED - conditions not met")
            logger.error(f"   all_steps_done: {getattr(state, 'all_steps_done', False)}")
            logger.error(f"   optimization_in_progress: {getattr(state, 'optimization_in_progress', False)}")
            logger.error(f"   optimization_complete: {getattr(state, 'optimization_complete', False)}")
        
        return should_run_stage8
        
    except Exception as e:
        logger.error(f"Stage 8 detection logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stage8_function_import():
    """Test that Stage 8 function can be imported and called."""
    logger = logging.getLogger("Stage8IntegrationTest")
    
    try:
        logger.info("=" * 60)
        logger.info("TESTING STAGE 8 FUNCTION IMPORT")
        logger.info("=" * 60)
        
        # Test importing Stage 8 function
        from stages.stage8 import stage8_optimize_script
        logger.info("✅ stage8_optimize_script imported successfully")
        
        # Test importing from stages package
        from stages import stage8_optimize_script as stage8_from_package
        logger.info("✅ stage8_optimize_script imported from stages package")
        
        # Verify they're the same function
        if stage8_optimize_script == stage8_from_package:
            logger.info("✅ Both imports reference the same function")
        else:
            logger.warning("⚠️ Imports reference different functions")
        
        logger.info(f"Function location: {stage8_optimize_script.__module__}")
        logger.info(f"Function name: {stage8_optimize_script.__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"Stage 8 function import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combined_script_creation():
    """Test the combined script creation logic."""
    logger = logging.getLogger("Stage8IntegrationTest")
    
    try:
        logger.info("=" * 60)
        logger.info("TESTING COMBINED SCRIPT CREATION")
        logger.info("=" * 60)
        
        from state_manager import StateManager
        from stages.stage6 import create_final_step_script, create_combined_script
        
        # Create a state with test data
        state = StateManager()
        state.selected_test_case = {
            'Test Case ID': 'TC_TEST_001',
            'Test Case Name': 'Integration Test Case'
        }
        state.previous_scripts = {
            "1": "# Step 1 script\nprint('Step 1')",
            "2": "# Step 2 script\nprint('Step 2')",
            "3": "# Step 3 script\nprint('Step 3')"
        }
        
        logger.info(f"Test case ID: {state.selected_test_case['Test Case ID']}")
        logger.info(f"Previous scripts: {len(state.previous_scripts)} scripts")
        
        # Test final step script creation
        logger.info("Testing create_final_step_script...")
        final_script_path = create_final_step_script(state)
        
        if final_script_path:
            logger.info(f"✅ Final step script created: {final_script_path}")
            logger.info(f"   combined_script_path in state: {getattr(state, 'combined_script_path', 'NOT_SET')}")
            logger.info(f"   combined_script_content length: {len(getattr(state, 'combined_script_content', ''))}")
            
            # Verify file exists
            if os.path.exists(final_script_path):
                logger.info("✅ Final step script file exists on disk")
                with open(final_script_path, 'r') as f:
                    content = f.read()
                    logger.info(f"   File content length: {len(content)} chars")
            else:
                logger.error("❌ Final step script file does not exist on disk")
                return False
        else:
            logger.error("❌ Failed to create final step script")
            return False
        
        # Test combined script creation
        logger.info("Testing create_combined_script...")
        combined_script_path = create_combined_script(state)
        
        if combined_script_path:
            logger.info(f"✅ Combined script created: {combined_script_path}")
            
            # Verify file exists
            if os.path.exists(combined_script_path):
                logger.info("✅ Combined script file exists on disk")
                with open(combined_script_path, 'r') as f:
                    content = f.read()
                    logger.info(f"   File content length: {len(content)} chars")
            else:
                logger.error("❌ Combined script file does not exist on disk")
                return False
        else:
            logger.error("❌ Failed to create combined script")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Combined script creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_integration():
    """Test the complete app.py integration."""
    logger = logging.getLogger("Stage8IntegrationTest")
    
    try:
        logger.info("=" * 60)
        logger.info("TESTING APP.PY INTEGRATION")
        logger.info("=" * 60)
        
        # Test app.py import
        import app
        logger.info("✅ app.py imported successfully")
        
        # Test that run_app function exists
        if hasattr(app, 'run_app'):
            logger.info("✅ run_app function exists in app.py")
        else:
            logger.error("❌ run_app function not found in app.py")
            return False
        
        # Test stage imports in app.py
        try:
            from stages import stage8_optimize_script
            logger.info("✅ stage8_optimize_script can be imported (as used in app.py)")
        except ImportError as e:
            logger.error(f"❌ Failed to import stage8_optimize_script: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"App integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    logger = setup_logging()
    
    logger.info("🧪 STAGE 8 INTEGRATION FIX - COMPREHENSIVE TEST")
    logger.info("=" * 80)
    logger.info("Testing the complete Stage 8 integration after applying fixes...")
    
    tests = [
        ("Stage 8 Detection Logic", test_stage8_detection_logic),
        ("Stage 8 Function Import", test_stage8_function_import),
        ("Combined Script Creation", test_combined_script_creation),
        ("App.py Integration", test_app_integration),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("INTEGRATION TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1
    
    overall_status = "✅ ALL TESTS PASSED" if passed == total else f"❌ {total - passed} TESTS FAILED"
    logger.info(f"\nOverall Result: {overall_status} ({passed}/{total})")
    
    if passed == total:
        logger.info("\n🎉 INTEGRATION FIX SUCCESSFUL!")
        logger.info("The Stage 8 integration should now work correctly.")
        logger.info("Next steps:")
        logger.info("1. Run the main application: streamlit run app.py")
        logger.info("2. Complete Stages 1-7 to reach Stage 8")
        logger.info("3. Verify the 'Start Script Optimization' button appears")
        logger.info("4. Click the button and verify optimization process starts")
    else:
        logger.info("\n🔧 INTEGRATION FIX NEEDS ATTENTION")
        logger.info("Some tests failed. Please review the errors above.")
        
        failed_tests = [name for name, success in results.items() if not success]
        logger.info(f"Failed tests: {', '.join(failed_tests)}")
    
    logger.info("=" * 80)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
