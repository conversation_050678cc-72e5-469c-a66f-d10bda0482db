#!/usr/bin/env python3
"""
Live test for Stage 8 button click issue.

This script sets up a minimal Streamlit app to test the Stage 8 button functionality
in isolation to identify the exact issue.
"""

import streamlit as st
import logging
import os
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Stage8LiveTest")

# Add the current directory to the Python path
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_test_state():
    """Set up a test state with all required prerequisites."""
    from state_manager import StateManager
    
    # Initialize state manager
    state = StateManager()
    state.init_in_session(st)
    
    # Set up prerequisites for Stage 8
    state.google_api_key = "test_api_key_AIza123456789"
    state.combined_script_content = """# Test combined script
import pytest
from selenium import webdriver

def test_example():
    driver = webdriver.Chrome()
    driver.get("https://example.com")
    assert "Example" in driver.title
    driver.quit()
"""
    state.combined_script_path = "test_combined_script.py"
    state.selected_test_case = {
        "Test Case ID": "TC001",
        "Test Case Name": "Test Case Example",
        "Steps": [
            {"Step No": 1, "Action": "Navigate", "Expected Result": "Page loads"}
        ]
    }
    
    # Store in session state
    st.session_state['state'] = state
    
    return state

def test_stage8_button():
    """Test the Stage 8 button functionality in isolation."""
    st.title("🧪 Stage 8 Button Live Test")
    
    # Set up test state
    state = setup_test_state()
    
    # Display current state
    st.subheader("📊 Current State")
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Prerequisites:**")
        st.write(f"- Google API Key: {'✅ SET' if state.google_api_key else '❌ NOT SET'}")
        st.write(f"- Combined Script: {'✅ SET' if state.combined_script_content else '❌ NOT SET'}")
        st.write(f"- Script Path: {'✅ SET' if state.combined_script_path else '❌ NOT SET'}")
        st.write(f"- Test Case: {'✅ SET' if state.selected_test_case else '❌ NOT SET'}")
    
    with col2:
        st.write("**Optimization Flags:**")
        st.write(f"- optimization_in_progress: {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
        st.write(f"- optimization_complete: {getattr(state, 'optimization_complete', 'NOT_SET')}")
        st.write(f"- optimization_start_time: {getattr(state, 'optimization_start_time', 'NOT_SET')}")
    
    # Display session state info
    st.subheader("🔍 Session State Debug")
    st.write(f"Session state keys: {list(st.session_state.keys())}")
    st.write(f"optimization_in_progress in session: {st.session_state.get('optimization_in_progress', 'NOT_FOUND')}")
    
    # Test button
    st.subheader("🔘 Button Test")
    
    if st.button("🧪 Test Stage 8 Button Logic", use_container_width=True, key="test_stage8_button"):
        logger.info("=" * 60)
        logger.info("LIVE TEST: Button clicked!")
        logger.info("=" * 60)
        
        # Show immediate feedback
        with st.spinner("Processing button click..."):
            st.info("🔄 Button click detected - processing...")
            
            # Validate prerequisites
            logger.info("Validating prerequisites...")
            if not state.google_api_key:
                st.error("❌ Missing Google API key")
                logger.error("Missing Google API key")
                return
            
            if not state.combined_script_content:
                st.error("❌ Missing combined script content")
                logger.error("Missing combined script content")
                return
            
            logger.info("Prerequisites validated successfully")
            
            # Set optimization flags
            logger.info("Setting optimization flags...")
            state.optimization_in_progress = True
            state.optimization_start_time = datetime.now()
            state.optimization_complete = False
            
            # Set session state flag
            st.session_state['optimization_in_progress'] = True
            st.session_state['state'] = state
            
            logger.info(f"Flags set - optimization_in_progress: {state.optimization_in_progress}")
            logger.info(f"Session state flag: {st.session_state['optimization_in_progress']}")
            
            st.success("✅ Optimization flags set successfully!")
            st.info("🔄 About to call st.rerun()...")
            
            logger.info("Calling st.rerun()...")
            st.rerun()
    
    # Check if we're in optimization mode
    if getattr(state, 'optimization_in_progress', False) or st.session_state.get('optimization_in_progress', False):
        st.subheader("🚀 Optimization Mode Detected!")
        st.success("✅ The button click worked! Optimization flags are set.")
        
        # Show optimization simulation
        with st.expander("🔧 Optimization Simulation", expanded=True):
            st.write("This is where the actual optimization would happen.")
            st.code(state.combined_script_content, language="python")
            
            if st.button("🏁 Complete Optimization", key="complete_optimization"):
                state.optimization_in_progress = False
                state.optimization_complete = True
                
                # Clear session state flag
                if 'optimization_in_progress' in st.session_state:
                    del st.session_state['optimization_in_progress']
                
                st.session_state['state'] = state
                st.success("✅ Optimization completed!")
                st.rerun()
    
    elif getattr(state, 'optimization_complete', False):
        st.subheader("🎉 Optimization Complete!")
        st.success("✅ The optimization process completed successfully.")
        
        if st.button("🔄 Reset Test", key="reset_test"):
            state.optimization_in_progress = False
            state.optimization_complete = False
            state.optimization_start_time = None
            
            # Clear session state flags
            if 'optimization_in_progress' in st.session_state:
                del st.session_state['optimization_in_progress']
            
            st.session_state['state'] = state
            st.rerun()

def main():
    """Main function for the live test."""
    st.set_page_config(
        page_title="Stage 8 Live Test",
        page_icon="🧪",
        layout="wide"
    )
    
    # Add custom CSS for better visibility
    st.markdown("""
    <style>
    .stButton > button {
        background-color: #4CAF50;
        color: white;
        border-radius: 8px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background-color: #45a049;
    }
    </style>
    """, unsafe_allow_html=True)
    
    test_stage8_button()

if __name__ == "__main__":
    main()
