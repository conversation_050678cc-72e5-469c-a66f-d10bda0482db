#!/usr/bin/env python3
"""
Debug script for Stage 8 button click issue.

This script helps diagnose why the "Start Script Optimization" button
is not working as expected in GretahAI ScriptWeaver.
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Set up comprehensive logging for debugging."""
    # Create logs directory if it doesn't exist
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # Create a timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(logs_dir, f"stage8_debug_{timestamp}.log")
    
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger("Stage8Debug")
    logger.info(f"Debug logging started - log file: {log_file}")
    return logger, log_file

def check_state_manager():
    """Check if StateManager is working correctly."""
    logger = logging.getLogger("Stage8Debug")
    
    try:
        from state_manager import StateManager
        
        logger.info("Creating StateManager instance...")
        state = StateManager()
        
        logger.info(f"StateManager created successfully: {type(state)}")
        logger.info(f"StateManager attributes: {[attr for attr in dir(state) if not attr.startswith('_')]}")
        
        # Test setting optimization flags
        logger.info("Testing optimization flag setting...")
        state.optimization_in_progress = True
        state.optimization_start_time = datetime.now()
        state.optimization_complete = False
        
        logger.info(f"optimization_in_progress: {state.optimization_in_progress}")
        logger.info(f"optimization_start_time: {state.optimization_start_time}")
        logger.info(f"optimization_complete: {state.optimization_complete}")
        
        return True, state
        
    except Exception as e:
        logger.error(f"StateManager test failed: {e}", exc_info=True)
        return False, None

def check_stage8_module():
    """Check if Stage 8 module can be imported and functions exist."""
    logger = logging.getLogger("Stage8Debug")
    
    try:
        from stages.stage8 import stage8_optimize_script
        
        logger.info("Stage 8 module imported successfully")
        logger.info(f"stage8_optimize_script function: {stage8_optimize_script}")
        
        return True
        
    except Exception as e:
        logger.error(f"Stage 8 module import failed: {e}", exc_info=True)
        return False

def check_app_module():
    """Check if app.py can be imported and run_app function exists."""
    logger = logging.getLogger("Stage8Debug")
    
    try:
        from app import run_app
        
        logger.info("App module imported successfully")
        logger.info(f"run_app function: {run_app}")
        
        return True
        
    except Exception as e:
        logger.error(f"App module import failed: {e}", exc_info=True)
        return False

def simulate_button_click_logic():
    """Simulate the button click logic to see where it fails."""
    logger = logging.getLogger("Stage8Debug")
    
    try:
        from state_manager import StateManager
        
        logger.info("=" * 60)
        logger.info("SIMULATING BUTTON CLICK LOGIC")
        logger.info("=" * 60)
        
        # Create a state manager
        state = StateManager()
        
        # Simulate having the required prerequisites
        state.google_api_key = "test_api_key_AIza123456789"
        state.combined_script_content = "# Test script content\nprint('Hello World')"
        state.combined_script_path = "test_combined_script.py"
        state.selected_test_case = {"Test Case ID": "TC001", "Test Case Name": "Test Case"}
        
        logger.info("Prerequisites set up:")
        logger.info(f"  - google_api_key: {'SET' if state.google_api_key else 'NOT_SET'}")
        logger.info(f"  - combined_script_content length: {len(state.combined_script_content)}")
        logger.info(f"  - combined_script_path: {state.combined_script_path}")
        logger.info(f"  - selected_test_case: {state.selected_test_case.get('Test Case ID')}")
        
        # Simulate the button click logic
        logger.info("Simulating button click validation...")
        
        # Check Google API key
        if not hasattr(state, 'google_api_key') or not state.google_api_key:
            logger.error("❌ Button click would fail: Missing Google AI API key")
            return False
        else:
            logger.info("✅ Google AI API key check passed")
        
        # Check combined script content
        if not hasattr(state, 'combined_script_content') or not state.combined_script_content:
            logger.error("❌ Button click would fail: Missing combined script content")
            return False
        else:
            logger.info("✅ Combined script content check passed")
        
        # Simulate setting optimization flags
        logger.info("Simulating optimization flag setting...")
        state.optimization_in_progress = True
        state.optimization_start_time = datetime.now()
        state.optimization_complete = False
        
        logger.info(f"✅ Flags set successfully:")
        logger.info(f"  - optimization_in_progress: {state.optimization_in_progress}")
        logger.info(f"  - optimization_start_time: {state.optimization_start_time}")
        logger.info(f"  - optimization_complete: {state.optimization_complete}")
        
        # Simulate session state update
        mock_session_state = {}
        mock_session_state['optimization_in_progress'] = True
        mock_session_state['state'] = state
        
        logger.info(f"✅ Mock session state updated:")
        logger.info(f"  - optimization_in_progress: {mock_session_state['optimization_in_progress']}")
        logger.info(f"  - state object stored: {type(mock_session_state['state'])}")
        
        # Simulate app.py Stage 8 detection logic
        logger.info("Simulating app.py Stage 8 detection logic...")
        
        should_run_stage8 = False
        
        # Check condition 2: Optimization is in progress
        if ((hasattr(state, 'optimization_in_progress') and state.optimization_in_progress) or
            mock_session_state.get('optimization_in_progress', False)):
            should_run_stage8 = True
            logger.info("✅ Stage 8 detection logic would trigger (optimization in progress)")
        else:
            logger.error("❌ Stage 8 detection logic would NOT trigger")
            logger.error(f"  - state.optimization_in_progress: {getattr(state, 'optimization_in_progress', 'NOT_SET')}")
            logger.error(f"  - session_state optimization_in_progress: {mock_session_state.get('optimization_in_progress', 'NOT_SET')}")
        
        logger.info("=" * 60)
        logger.info(f"SIMULATION RESULT: {'SUCCESS' if should_run_stage8 else 'FAILURE'}")
        logger.info("=" * 60)
        
        return should_run_stage8
        
    except Exception as e:
        logger.error(f"Button click simulation failed: {e}", exc_info=True)
        return False

def main():
    """Main debug function."""
    logger, log_file = setup_logging()
    
    logger.info("=" * 80)
    logger.info("STAGE 8 BUTTON CLICK DEBUG SESSION STARTED")
    logger.info("=" * 80)
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Run diagnostic checks
    checks = [
        ("StateManager", check_state_manager),
        ("Stage 8 Module", check_stage8_module),
        ("App Module", check_app_module),
    ]
    
    results = {}
    for check_name, check_func in checks:
        logger.info(f"\n--- Running {check_name} Check ---")
        if check_name == "StateManager":
            success, state = check_func()
            results[check_name] = success
        else:
            results[check_name] = check_func()
    
    # Run button click simulation
    logger.info(f"\n--- Running Button Click Simulation ---")
    results["Button Click Simulation"] = simulate_button_click_logic()
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("DIAGNOSTIC SUMMARY")
    logger.info("=" * 80)
    
    for check_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{check_name}: {status}")
    
    all_passed = all(results.values())
    overall_status = "✅ ALL CHECKS PASSED" if all_passed else "❌ SOME CHECKS FAILED"
    logger.info(f"\nOverall Status: {overall_status}")
    
    if not all_passed:
        logger.info("\n🔧 RECOMMENDED ACTIONS:")
        if not results.get("StateManager", True):
            logger.info("- Fix StateManager import/initialization issues")
        if not results.get("Stage 8 Module", True):
            logger.info("- Fix Stage 8 module import issues")
        if not results.get("App Module", True):
            logger.info("- Fix app.py import issues")
        if not results.get("Button Click Simulation", True):
            logger.info("- Debug button click logic and state management")
    
    logger.info(f"\n📄 Full debug log saved to: {log_file}")
    logger.info("=" * 80)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
